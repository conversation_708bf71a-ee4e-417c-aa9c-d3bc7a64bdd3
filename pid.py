import wiringpi
import time
import threading
from PWM import pwm_x, pwm_y, pwm_set
from red_laser_detector import get_laser_coordinates, get_center_coordinates

# PID参数
ki = 0.1
kp = 0.1
kd = 0.1

# PID状态变量
last_error_x = 0
last_error_y = 0
integral_x = 0
integral_y = 0

# 全局变量引用
X = 0
Y = 0



def pid(x,y):
    global X, Y, last_error_x, last_error_y, integral_x, integral_y
    a = 0.1
    error_x = X-x
    error_y = Y-y
    error_difference_x = error_x - last_error_x
    error_difference_y = error_y - last_error_y
    integral_x += error_x*a + error_difference_x*(1-a)
    integral_y += error_y*a + error_difference_y*(1-a)
    output_x = kp * error_x + ki * integral_x + kd * error_difference_x
    output_y = kp * error_y + ki * integral_y + kd * error_difference_y
    last_error_x = error_x
    last_error_y = error_y

    # 限制PWM输出范围 (250-1250)
    output_x = max(250, min(1250, int(750 + output_x)))
    output_y = max(250, min(1250, int(750 + output_y)))

    pwm_x(output_x)
    pwm_y(output_y)
    print(output_x, output_y)

# ==================== 单进程多线程PID控制系统 ====================

# PID控制线程相关变量
pid_thread = None
pid_running = False
pid_target_x = 0
pid_target_y = 0
pid_thread_lock = threading.Lock()

def pid_control_thread():
    """
    PID控制线程函数 - 30Hz频率运行您的pid函数
    """
    global X, Y, last_error_x, last_error_y, integral_x, integral_y
    global pid_running, pid_target_x, pid_target_y

    print(f"🚀 PID控制线程启动 - 目标位置: ({pid_target_x}, {pid_target_y})")

    # 初始化PWM
    try:
        pwm_set()
        print("✅ PWM初始化成功")
    except Exception as e:
        print(f"❌ PWM初始化失败: {e}")
        return

    # 重置PID状态
    last_error_x = 0
    last_error_y = 0
    integral_x = 0
    integral_y = 0

    # 30Hz控制频率
    control_period = 1.0 / 30.0  # 33.33ms

    while pid_running:
        start_time = time.time()

        try:
            # 获取当前激光点坐标
            current_X, current_Y = get_laser_coordinates()
            X = current_X
            Y = current_Y

            # 获取目标坐标
            with pid_thread_lock:
                target_x = pid_target_x
                target_y = pid_target_y

            # 调用您的pid函数
            pid(target_x, target_y)

            # 控制频率为30Hz
            elapsed = time.time() - start_time
            sleep_time = control_period - elapsed
            if sleep_time > 0:
                time.sleep(sleep_time)

        except Exception as e:
            print(f"❌ PID控制线程异常: {e}")
            break

    # 停止时输出750到PWM
    print("🛑 PID控制线程停止，输出750停止舵机")
    try:
        pwm_x(750)
        pwm_y(750)
    except Exception as e:
        print(f"❌ 停止PWM输出失败: {e}")

def start_pid_control_thread(target_x, target_y):
    """
    启动PID控制线程
    :param target_x: 目标X坐标
    :param target_y: 目标Y坐标
    :return: True if started successfully, False otherwise
    """
    global pid_thread, pid_running, pid_target_x, pid_target_y

    if pid_running:
        print("⚠️  PID控制线程已在运行")
        return False

    # 设置目标坐标
    with pid_thread_lock:
        pid_target_x = target_x
        pid_target_y = target_y

    # 启动控制线程
    pid_running = True
    pid_thread = threading.Thread(target=pid_control_thread, daemon=True)
    pid_thread.start()

    print(f"✅ PID控制线程已启动，目标位置: ({target_x}, {target_y})")
    return True

def stop_pid_control_thread():
    """
    停止PID控制线程
    """
    global pid_thread, pid_running

    if not pid_running:
        print("⚠️  PID控制线程未运行")
        return

    print("🛑 正在停止PID控制线程...")
    pid_running = False

    if pid_thread and pid_thread.is_alive():
        pid_thread.join(timeout=2.0)
        if pid_thread.is_alive():
            print("⚠️  PID控制线程停止超时")
        else:
            print("✅ PID控制线程已停止")

    pid_thread = None

def is_pid_running():
    """
    检查PID控制是否正在运行
    :return: True if running, False otherwise
    """
    return pid_running

def update_pid_target(target_x, target_y):
    """
    更新PID目标位置
    :param target_x: 新的目标X坐标
    :param target_y: 新的目标Y坐标
    """
    global pid_target_x, pid_target_y

    with pid_thread_lock:
        pid_target_x = target_x
        pid_target_y = target_y

    print(f"🎯 PID目标位置已更新: ({target_x}, {target_y})")

