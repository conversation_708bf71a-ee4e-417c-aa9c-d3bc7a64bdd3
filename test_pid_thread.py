#!/usr/bin/env python3
"""
单进程多线程PID控制系统测试脚本
"""

import time
import cv2
from red_laser_detector import RedLaserDetector, get_laser_coordinates, get_center_coordinates
from pid import start_pid_control_thread, stop_pid_control_thread, is_pid_running

def test_pid_thread():
    """测试单进程多线程PID控制系统"""
    print("=== 单进程多线程PID控制系统测试 ===")
    
    try:
        # 1. 初始化激光点检测器
        print("\n1. 初始化激光点检测器")
        laser_detector = RedLaserDetector()
        
        # 设置测试参数（模拟标定结果）
        test_bounds = (400, 200, 600, 400)
        test_center = (700, 400)
        laser_detector.set_reference_data(test_bounds, test_center)
        
        # 检测激光点（模拟当前位置）
        frame = cv2.imread('try1.jpg')
        if frame is not None:
            laser_point = laser_detector.detect_red_laser(frame)
            print(f"当前激光点位置: {laser_point}")
        else:
            print("⚠️  无法加载测试图像，使用模拟数据")
        
        # 2. 测试PID线程启动
        print("\n2. 测试PID线程启动")
        x0, y0 = get_center_coordinates()
        print(f"目标位置: x0={x0}, y0={y0}")
        
        # 启动PID控制线程
        if start_pid_control_thread(x0, y0):
            print("✅ PID控制线程已启动")
        else:
            print("❌ PID控制线程启动失败")
            return False
        
        # 3. 检查运行状态
        print(f"\n3. PID运行状态检查: {is_pid_running()}")
        
        # 4. 模拟运行5秒
        print("\n4. 模拟运行5秒，观察PID控制")
        for i in range(5):
            current_X, current_Y = get_laser_coordinates()
            print(f"第{i+1}秒: 当前位置=({current_X}, {current_Y}), 目标位置=({x0}, {y0}), 运行状态={is_pid_running()}")
            time.sleep(1)
        
        # 5. 停止PID控制线程
        print("\n5. 停止PID控制线程")
        stop_pid_control_thread()
        
        # 6. 验证停止状态
        time.sleep(0.5)
        print(f"停止后运行状态: {is_pid_running()}")
        
        print("\n🎉 单进程多线程PID控制系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_keyboard_control_simulation():
    """模拟键盘控制测试"""
    print("\n=== 键盘控制模拟测试 ===")
    
    try:
        # 模拟按'a'键启动PID控制的流程
        print("模拟按'a'键启动PID控制...")
        
        # 1. 检查是否已标定（模拟标定完成状态）
        print("✅ 模拟标定完成状态")
        
        # 2. 获取中心点坐标
        from red_laser_detector import RedLaserDetector
        detector = RedLaserDetector()
        detector.set_reference_data((400, 200, 600, 400), (700, 400))
        x0, y0 = get_center_coordinates()
        print(f"获取目标位置: x0={x0}, y0={y0}")
        
        # 3. 启动PID控制（模拟按'a'键）
        print("启动PID控制线程...")
        if start_pid_control_thread(x0, y0):
            print("✅ PID控制线程启动成功")
        else:
            print("❌ PID控制线程启动失败")
            return False
        
        # 4. 运行2秒
        print("PID控制运行中...")
        for i in range(2):
            print(f"运行状态: {is_pid_running()}")
            time.sleep(1)
        
        # 5. 停止PID控制（模拟再次按'a'键）
        print("停止PID控制线程...")
        stop_pid_control_thread()
        
        # 6. 验证停止
        time.sleep(0.5)
        print(f"最终运行状态: {is_pid_running()}")
        
        print("✅ 键盘控制模拟测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 键盘控制测试失败: {e}")
        return False

def test_camera_compatibility():
    """测试摄像头兼容性"""
    print("\n=== 摄像头兼容性测试 ===")
    
    try:
        # 测试摄像头是否能正常打开
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ 摄像头打开成功")
            
            # 读取一帧
            ret, frame = cap.read()
            if ret:
                print(f"✅ 摄像头读取成功，帧尺寸: {frame.shape}")
            else:
                print("❌ 摄像头读取失败")
                return False
            
            cap.release()
        else:
            print("❌ 摄像头打开失败")
            return False
        
        print("✅ 摄像头兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("单进程多线程PID控制系统完整测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行测试
    test_results.append(("摄像头兼容性测试", test_camera_compatibility()))
    test_results.append(("单进程多线程PID控制测试", test_pid_thread()))
    test_results.append(("键盘控制模拟测试", test_keyboard_control_simulation()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！单进程多线程PID控制系统准备就绪")
        print("\n📋 使用说明:")
        print("   1. 运行 sudo python3 main.py")
        print("   2. 按'c'或'p'键完成矩形标定")
        print("   3. 按'a'键启动PID控制（激光点移动到中心点）")
        print("   4. 再次按'a'键停止PID控制")
        print("   5. 按'q'键退出程序")
        print("\n🔧 技术特点:")
        print("   - 单进程多线程架构，避免摄像头冲突")
        print("   - 30Hz精确控制频率")
        print("   - 自动获取激光点坐标，无需手动数据共享")
        print("   - 线程安全的启动/停止控制")
    else:
        print("⚠️  部分测试失败，请检查系统配置")

if __name__ == '__main__':
    main()
